package impl

import (
	"context"

	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// CopyrightRepositoryImpl 著作权仓储实现
type CopyrightRepositoryImpl struct {
	db           *gorm.DB
	cache        cache.Manager
	cacheKeyBase string
}

// NewCopyrightRepository 创建著作权仓储实例
func NewCopyrightRepository(db *gorm.DB, cache cache.Manager) repository.CopyrightRepository {
	return &CopyrightRepositoryImpl{
		db:           db,
		cache:        cache,
		cacheKeyBase: "copyright:",
	}
}

// Create 创建著作权
func (r *CopyrightRepositoryImpl) Create(ctx context.Context, copyright *model.Copyright) error {
	return r.db.WithContext(ctx).Create(copyright).Error
}

// GetByKSUID 根据KSUID获取著作权
func (r *CopyrightRepositoryImpl) GetByKSUID(ctx context.Context, copyrightKSUID string) (*model.Copyright, error) {
	var copyright model.Copyright
	err := r.db.WithContext(ctx).Where("copyright_ksuid = ?", copyrightKSUID).First(&copyright).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repository.ErrCopyrightNotFound
		}
		return nil, err
	}
	return &copyright, nil
}

// Update 更新著作权
func (r *CopyrightRepositoryImpl) Update(ctx context.Context, copyright *model.Copyright) error {
	return r.db.WithContext(ctx).Save(copyright).Error
}

// Delete 删除著作权
func (r *CopyrightRepositoryImpl) Delete(ctx context.Context, copyrightKSUID string) error {
	return r.db.WithContext(ctx).Where("copyright_ksuid = ?", copyrightKSUID).Delete(&model.Copyright{}).Error
}

// GetByRightsKSUID 根据权益认证KSUID获取著作权
func (r *CopyrightRepositoryImpl) GetByRightsKSUID(ctx context.Context, rightsKSUID string) (*model.Copyright, error) {
	var copyright model.Copyright
	err := r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).First(&copyright).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repository.ErrCopyrightNotFound
		}
		return nil, err
	}
	return &copyright, nil
}

// GetByUserKSUID 根据用户KSUID获取著作权列表
func (r *CopyrightRepositoryImpl) GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.Copyright, int64, error) {
	var copyrights []*model.Copyright
	var total int64

	query := r.db.WithContext(ctx).
		Joins("JOIN complaint_rights_verifications ON complaint_copyrights.rights_ksuid = complaint_rights_verifications.rights_ksuid").
		Where("complaint_rights_verifications.user_ksuid = ?", userKSUID)

	// 获取总数
	if err := query.Model(&model.Copyright{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&copyrights).Error; err != nil {
		return nil, 0, err
	}

	return copyrights, total, nil
}
