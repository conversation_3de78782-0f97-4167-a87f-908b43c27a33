package impl

import (
	"context"

	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// TrademarkRepositoryImpl 商标权仓储实现
type TrademarkRepositoryImpl struct {
	db           *gorm.DB
	cache        cache.Manager
	cacheKeyBase string
}

// NewTrademarkRepository 创建商标权仓储实例
func NewTrademarkRepository(db *gorm.DB, cache cache.Manager) repository.TrademarkRepository {
	return &TrademarkRepositoryImpl{
		db:           db,
		cache:        cache,
		cacheKeyBase: "trademark:",
	}
}

// Create 创建商标权
func (r *TrademarkRepositoryImpl) Create(ctx context.Context, trademark *model.Trademark) error {
	return r.db.WithContext(ctx).Create(trademark).Error
}

// GetByKSUID 根据KSUID获取商标权
func (r *TrademarkRepositoryImpl) GetByKSUID(ctx context.Context, trademarkKSUID string) (*model.Trademark, error) {
	var trademark model.Trademark
	err := r.db.WithContext(ctx).Where("trademark_ksuid = ?", trademarkKSUID).First(&trademark).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repository.ErrTrademarkNotFound
		}
		return nil, err
	}
	return &trademark, nil
}

// Update 更新商标权
func (r *TrademarkRepositoryImpl) Update(ctx context.Context, trademark *model.Trademark) error {
	return r.db.WithContext(ctx).Save(trademark).Error
}

// Delete 删除商标权
func (r *TrademarkRepositoryImpl) Delete(ctx context.Context, trademarkKSUID string) error {
	return r.db.WithContext(ctx).Where("trademark_ksuid = ?", trademarkKSUID).Delete(&model.Trademark{}).Error
}

// GetByRightsKSUID 根据权益认证KSUID获取商标权
func (r *TrademarkRepositoryImpl) GetByRightsKSUID(ctx context.Context, rightsKSUID string) (*model.Trademark, error) {
	var trademark model.Trademark
	err := r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).First(&trademark).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repository.ErrTrademarkNotFound
		}
		return nil, err
	}
	return &trademark, nil
}

// GetByUserKSUID 根据用户KSUID获取商标权列表
func (r *TrademarkRepositoryImpl) GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.Trademark, int64, error) {
	var trademarks []*model.Trademark
	var total int64

	query := r.db.WithContext(ctx).
		Joins("JOIN complaint_rights_verifications ON complaint_trademarks.rights_ksuid = complaint_rights_verifications.rights_ksuid").
		Where("complaint_rights_verifications.user_ksuid = ?", userKSUID)

	// 获取总数
	if err := query.Model(&model.Trademark{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&trademarks).Error; err != nil {
		return nil, 0, err
	}

	return trademarks, total, nil
}
