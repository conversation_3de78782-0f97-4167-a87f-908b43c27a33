# 投诉服务配置文件

# 服务器配置
server:
  port: 8080
  mode: debug # debug, release
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# 数据库配置
database:
  driver: mysql
  host: localhost
  port: 3306
  username: root
  password: password
  database: pxpat_complaint
  charset: utf8mb4
  parse_time: true
  loc: Local
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600s
  log_level: info

# Redis配置
redis:
  addr: localhost:6379
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s
  pool_timeout: 4s
  idle_timeout: 300s

# RabbitMQ配置
rabbitmq:
  url: amqp://guest:guest@localhost:5672/
  exchange: pxpat.complaint
  queue_prefix: complaint
  consumer_count: 5
  prefetch_count: 10

# JWT配置
jwt:
  secret: your-jwt-secret-key
  expire_time: 24h
  refresh_expire_time: 168h # 7天
  issuer: pxpat-complaint-service

# 日志配置
log:
  level: info
  format: json
  output: stdout
  file_path: logs/complaint-service.log
  max_size: 100
  max_backups: 10
  max_age: 30
  compress: true

# Consul配置
consul:
  address: localhost:8500
  scheme: http
  datacenter: dc1
  service_name: complaint-service
  service_id: complaint-service-1
  service_port: 8080
  health_check_interval: 10s
  health_check_timeout: 3s
  health_check_deregister_critical_after: 30s

# 业务配置
complaint:
  # 限制配置
  limits:
    title_max_length: 255
    description_max_length: 5000
    max_evidence_files: 10
    max_complaints_per_day: 10
    max_rights_per_user: 5

  # 缓存配置
  cache:
    complaint_ttl: 1h
    identity_ttl: 24h
    rights_ttl: 24h
    country_ttl: 168h # 7天
    trademark_ttl: 168h # 7天
    violation_ttl: 168h # 7天

  # 文件上传配置
  file_upload:
    max_file_size: 10485760 # 10MB
    allowed_types:
      - image/jpeg
      - image/png
      - image/gif
      - application/pdf
      - application/msword
      - application/vnd.openxmlformats-officedocument.wordprocessingml.document
    max_files_per_upload: 5
    storage_path: complaint/evidence

  # 认证配置
  verification:
    auto_approve: false
    review_timeout: 72h # 3天
    certificate_valid_days: 365
    require_manual_review: true

# 安全配置
security:
  # CORS配置
  cors:
    allow_origins:
      - "*"
    allow_methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allow_headers:
      - "*"
    expose_headers:
      - "*"
    allow_credentials: true
    max_age: 86400

  # 限流配置
  rate_limit:
    enable: true
    requests_per_minute: 60
    burst_size: 10
    window_size: 1m

  # IP过滤配置
  enable_ip_filter: false
  ip_whitelist: []
  ip_blacklist: []

# 存储配置
storage:
  minio:
    endpoint: localhost:9000
    access_key: minioadmin
    secret_key: minioadmin
    bucket_name: pxpat-complaint
    use_ssl: false
    region: us-east-1

# 通知配置
notification:
  email:
    enable: false
    smtp_host: smtp.example.com
    smtp_port: 587
    username: <EMAIL>
    password: password
    from_addr: <EMAIL>

  sms:
    enable: false
    provider: aliyun
    access_key: your-access-key
    secret_key: your-secret-key
    sign_name: 投诉服务

# 监控配置
monitoring:
  metrics:
    enable: true
    endpoint: /metrics
    interval: 15s

  tracing:
    enable: false
    endpoint: http://localhost:14268/api/traces
    sample_rate: 0.1

  health:
    enable: true
    interval: 30s
    timeout: 5s
