package impl

import (
	"context"

	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// RightsVerificationRepositoryImpl 权益认证仓储实现
type RightsVerificationRepositoryImpl struct {
	db           *gorm.DB
	cache        cache.Manager
	cacheKeyBase string
}

// NewRightsVerificationRepository 创建权益认证仓储实例
func NewRightsVerificationRepository(db *gorm.DB, cache cache.Manager) repository.RightsVerificationRepository {
	return &RightsVerificationRepositoryImpl{
		db:           db,
		cache:        cache,
		cacheKeyBase: "rights_verification:",
	}
}

// Create 创建权益认证
func (r *RightsVerificationRepositoryImpl) Create(ctx context.Context, verification *model.RightsVerification) error {
	return r.db.WithContext(ctx).Create(verification).Error
}

// GetByKSUID 根据KSUID获取权益认证
func (r *RightsVerificationRepositoryImpl) GetByKSUID(ctx context.Context, rightsKSUID string) (*model.RightsVerification, error) {
	var verification model.RightsVerification
	err := r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).First(&verification).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, repository.ErrRightsVerificationNotFound
		}
		return nil, err
	}
	return &verification, nil
}

// Update 更新权益认证
func (r *RightsVerificationRepositoryImpl) Update(ctx context.Context, verification *model.RightsVerification) error {
	return r.db.WithContext(ctx).Save(verification).Error
}

// Delete 删除权益认证
func (r *RightsVerificationRepositoryImpl) Delete(ctx context.Context, rightsKSUID string) error {
	return r.db.WithContext(ctx).Where("rights_ksuid = ?", rightsKSUID).Delete(&model.RightsVerification{}).Error
}

// GetByUserKSUID 根据用户KSUID获取权益认证列表
func (r *RightsVerificationRepositoryImpl) GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.RightsVerification, int64, error) {
	var verifications []*model.RightsVerification
	var total int64

	query := r.db.WithContext(ctx).Where("user_ksuid = ?", userKSUID)

	// 获取总数
	if err := query.Model(&model.RightsVerification{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&verifications).Error; err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// GetWithFilters 根据过滤条件获取权益认证列表
func (r *RightsVerificationRepositoryImpl) GetWithFilters(ctx context.Context, filters repository.RightsFilters) ([]*model.RightsVerification, int64, error) {
	var verifications []*model.RightsVerification
	var total int64

	query := r.db.WithContext(ctx).Model(&model.RightsVerification{})

	// 应用过滤条件
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.UserKSUID != "" {
		query = query.Where("user_ksuid = ?", filters.UserKSUID)
	}
	if filters.ReviewerKSUID != "" {
		query = query.Where("reviewer_ksuid = ?", filters.ReviewerKSUID)
	}
	if filters.IsAgent != nil {
		query = query.Where("is_agent = ?", *filters.IsAgent)
	}
	if filters.StartDate != "" {
		query = query.Where("created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != "" {
		query = query.Where("created_at <= ?", filters.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用排序
	if filters.SortBy != "" {
		order := filters.SortBy
		if filters.SortOrder != "" {
			order += " " + filters.SortOrder
		}
		query = query.Order(order)
	} else {
		query = query.Order("created_at DESC")
	}

	// 应用分页
	if filters.PageSize > 0 {
		offset := (filters.Page - 1) * filters.PageSize
		query = query.Offset(offset).Limit(filters.PageSize)
	}

	if err := query.Find(&verifications).Error; err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// UpdateStatus 更新权益认证状态
func (r *RightsVerificationRepositoryImpl) UpdateStatus(ctx context.Context, rightsKSUID string, status model.RightsStatus, reviewerKSUID, reviewNote, rejectReason string) error {
	updates := map[string]interface{}{
		"status":         status,
		"reviewer_ksuid": reviewerKSUID,
		"review_note":    reviewNote,
	}
	
	if rejectReason != "" {
		updates["reject_reason"] = rejectReason
	}

	return r.db.WithContext(ctx).
		Model(&model.RightsVerification{}).
		Where("rights_ksuid = ?", rightsKSUID).
		Updates(updates).Error
}
