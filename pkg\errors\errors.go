package errors

import (
	errors2 "github.com/pkg/errors"
)

type Errors struct {
	Code         int `json:"code"`
	InternalCode int
	Err          error
}

func NewGlobalErrors(code int, internalCode int, err error) *Errors {
	return &Errors{
		Code:         code,
		InternalCode: internalCode,
		Err:          err,
	}
}

// 实现原生error接口
func (e *Errors) Error() string {
	if e.Err != nil {
		return e.Err.Error()
	} else {
		return "调用为空 error 请检查代码"
	}
}

func JudgeIsSuccessCode(code int) bool {
	if code > 9999 || code < 0 {
		return true
	}
	return false
}

func Is(err, target error) bool {
	return errors2.Is(err, target)
}

// New 创建新的错误
func New(code int, message string) *Errors {
	return &Errors{
		Code:         code,
		InternalCode: code,
		Err:          errors2.New(message),
	}
}

// Wrap 包装错误
func Wrap(err error, code int, message string) *Errors {
	return &Errors{
		Code:         code,
		InternalCode: code,
		Err:          errors2.Wrap(err, message),
	}
}

// NewValidationError 创建验证错误
func NewValidationError(message string) *Errors {
	return &Errors{
		Code:         INVALID_PARAMETER,
		InternalCode: INVALID_PARAMETER,
		Err:          errors2.New(message),
	}
}

// NewBusinessError 创建业务错误
func NewBusinessError(message string) *Errors {
	return &Errors{
		Code:         INVALID_PARAMETER,
		InternalCode: INVALID_PARAMETER,
		Err:          errors2.New(message),
	}
}

// NewInternalError 创建内部错误
func NewInternalError(message string) *Errors {
	return &Errors{
		Code:         SYSTEM_ERROR,
		InternalCode: SYSTEM_ERROR,
		Err:          errors2.New(message),
	}
}

// NewNotFoundError 创建未找到错误
func NewNotFoundError(message string) *Errors {
	return &Errors{
		Code:         FILE_NOT_FOUND,
		InternalCode: FILE_NOT_FOUND,
		Err:          errors2.New(message),
	}
}

// NewPermissionError 创建权限错误
func NewPermissionError(message string) *Errors {
	return &Errors{
		Code:         PERMISSION_DENIED,
		InternalCode: PERMISSION_DENIED,
		Err:          errors2.New(message),
	}
}

// NewBadRequestError 创建请求错误
func NewBadRequestError(message string) *Errors {
	return &Errors{
		Code:         INVALID_PARAMETER,
		InternalCode: INVALID_PARAMETER,
		Err:          errors2.New(message),
	}
}
